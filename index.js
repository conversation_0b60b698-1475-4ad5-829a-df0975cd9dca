const express = require('express');
const { MongoClient, ObjectId } = require('mongodb');

const app = express();
const port = 3000;

// Middleware to parse JSON
app.use(express.json());

// MongoDB connection URL - replace with your MongoDB connection string
const MONGODB_URI = 'mongodb://localhost:27017';
const DATABASE_NAME = 'hrms_db';
const COLLECTION_NAME = 'employees';

let db;
let collection;

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    const client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('Connected to MongoDB successfully');

    db = client.db(DATABASE_NAME);
    collection = db.collection(COLLECTION_NAME);
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

// Initialize MongoDB connection
connectToMongoDB();

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

// CREATE - Add a new employee
app.post('/employees', async (req, res) => {
  try {
    const employee = req.body;

    // Add timestamp
    employee.createdAt = new Date();
    employee.updatedAt = new Date();

    const result = await collection.insertOne(employee);

    res.status(201).json({
      success: true,
      message: 'Employee created successfully',
      data: {
        _id: result.insertedId,
        ...employee
      }
    });
  } catch (error) {
    console.error('Error creating employee:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating employee',
      error: error.message
    });
  }
});

// READ - Get all employees
app.get('/employees', async (req, res) => {
  try {
    const employees = await collection.find({}).toArray();

    res.json({
      success: true,
      message: 'Employees retrieved successfully',
      data: employees,
      count: employees.length
    });
  } catch (error) {
    console.error('Error fetching employees:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching employees',
      error: error.message
    });
  }
});

// READ - Get employee by ID
app.get('/employees/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Validate ObjectId
    if (!ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID format'
      });
    }

    const employee = await collection.findOne({ _id: new ObjectId(id) });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    res.json({
      success: true,
      message: 'Employee retrieved successfully',
      data: employee
    });
  } catch (error) {
    console.error('Error fetching employee:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching employee',
      error: error.message
    });
  }
});

// UPDATE - Update employee by ID
app.put('/employees/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Validate ObjectId
    if (!ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID format'
      });
    }

    // Add updated timestamp
    updateData.updatedAt = new Date();

    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    if (result.matchedCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    // Get updated employee
    const updatedEmployee = await collection.findOne({ _id: new ObjectId(id) });

    res.json({
      success: true,
      message: 'Employee updated successfully',
      data: updatedEmployee
    });
  } catch (error) {
    console.error('Error updating employee:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating employee',
      error: error.message
    });
  }
});

// DELETE - Delete employee by ID
app.delete('/employees/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Validate ObjectId
    if (!ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID format'
      });
    }

    const result = await collection.deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    res.json({
      success: true,
      message: 'Employee deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting employee:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting employee',
      error: error.message
    });
  }
});

// Start server
app.listen(port, () => {
  console.log(`HRMS Backend server listening at http://localhost:${port}`);
  console.log('Available endpoints:');
  console.log('  GET    /health           - Health check');
  console.log('  POST   /employees        - Create employee');
  console.log('  GET    /employees        - Get all employees');
  console.log('  GET    /employees/:id    - Get employee by ID');
  console.log('  PUT    /employees/:id    - Update employee by ID');
  console.log('  DELETE /employees/:id    - Delete employee by ID');
});