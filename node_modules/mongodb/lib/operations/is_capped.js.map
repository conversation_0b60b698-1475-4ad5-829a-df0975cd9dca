{"version": 3, "file": "is_capped.js", "sourceRoot": "", "sources": ["../../src/operations/is_capped.ts"], "names": [], "mappings": ";;;AACA,oCAAyC;AAGzC,2CAAuE;AAEvE,gBAAgB;AAChB,MAAa,iBAAkB,SAAQ,6BAA0B;IAI/D,YAAY,UAAsB,EAAE,OAAyB;QAC3D,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,IAAa,WAAW;QACtB,OAAO,iBAA0B,CAAC;IACpC,CAAC;IAEQ,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,OAAkC;QACvE,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;QAC7B,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;aACjC,eAAe,CACd,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,EAC7B,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,CACnF;aACA,OAAO,EAAE,CAAC;QACb,IAAI,UAAU,IAAI,IAAI,IAAI,UAAU,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YACrD,MAAM,IAAI,qBAAa,CAAC,cAAc,IAAI,CAAC,SAAS,YAAY,CAAC,CAAC;QACpE,CAAC;QACD,OAAO,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC;IACtC,CAAC;CACF;AA3BD,8CA2BC"}