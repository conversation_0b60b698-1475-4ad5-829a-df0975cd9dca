{"name": "finalhandler", "description": "Node.js final http responder", "version": "2.1.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "pillarjs/finalhandler", "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.26.0", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "^11.0.1", "nyc": "^17.1.0", "supertest": "^7.0.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-inspect": "mocha --reporter spec --inspect --inspect-brk test/"}}